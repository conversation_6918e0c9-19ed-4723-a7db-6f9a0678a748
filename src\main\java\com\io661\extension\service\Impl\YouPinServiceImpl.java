package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.io661.extension.commonURL.CommonYouPinHttpUrl;
import com.io661.extension.controller.SteamController;
import com.io661.extension.model.Steam.SteamRes;
import com.io661.extension.model.YouPin.*;
import com.io661.extension.service.YouPinService;
import lombok.Data;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.io661.extension.util.User.UserCookieManager.readToken;
import static com.io661.extension.util.YouPin.YouPinCookieManager.saveYouPinCookie;

@Data
public class YouPinServiceImpl implements YouPinService {

    private final String SessionId = "ZXbUfhuMmpsDACUIQnmdtZqh";

    private final CommonYouPinHttpUrl youPinHttpClient;
    private final Gson gson = new Gson();
    private AccountManagerServiceImpl accountManagerService;
    // Steam控制器
    private SteamController steamController;

    public YouPinServiceImpl() {
        this.youPinHttpClient = new CommonYouPinHttpUrl();
        this.accountManagerService = new AccountManagerServiceImpl();
        this.steamController = new SteamController();
    }

    /**
     * 发送短信验证码（1）
     *
     * @param phone 手机号
     * @return 返回结果数组，包含[状态码, 短信内容, 短信号码]
     */
    @Override
    public YouPinSendSmsRes sendSmsCode(String phone) throws IOException {

        YouPinSendSmsRes.SendSmsData resultData = new YouPinSendSmsRes.SendSmsData();
        YouPinSendSmsRes result = new YouPinSendSmsRes();

        Map<String, Object> data = new HashMap<>();
        data.put("Area", "86");
        data.put("Code", "");
        data.put("Mobile", phone);
        data.put("Sessionid", SessionId);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 发送POST请求
        String response1 = youPinHttpClient.doPost("user/Auth/SendSignInSmsCode", jsonBody, null);

        // 获取短信配置
        String response2 = youPinHttpClient.doGet("user/Auth/GetSmsUpSignInConfig", null, null);

        YouPinSendSmsRes youPinSendSmsRes = gson.fromJson(response2, YouPinSendSmsRes.class);

        // 如果code为0，就弹窗发送SmsUpContent到SmsUpNumber
        if (youPinSendSmsRes.getCode() != 0) {
            System.out.println("发送短信失败");
            return new YouPinSendSmsRes();
        }
        String msg = youPinSendSmsRes.getData().getSmsUpContent();
        String upNumber = youPinSendSmsRes.getData().getSmsUpNumber();

        resultData.setSmsUpContent(msg);
        resultData.setSmsUpNumber(upNumber);
        result.setCode(0);
        result.setData(resultData);

        return result;
    }

    /**
     * 登录账号（2）
     *
     * @param phone 手机号
     * @param code  验证码
     * @return 返回结果数组，包含[状态码, 消息, 令牌]
     */
    @Override
    public boolean loginAccount(String phone, String code) throws IOException {
        Map<String, Object> data = new HashMap<>();
        data.put("Area", "86");
        data.put("Code", code);
        data.put("DeviceName", "Xiaomi Mi10");
        data.put("Mobile", phone);
        data.put("Sessionid", SessionId);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 发送POST请求
        String response = youPinHttpClient.doPost("user/Auth/SmsUpSignIn", jsonBody, null);

        try {
            YouPinLoginRes youPinLoginRes = gson.fromJson(response, YouPinLoginRes.class);
            if (youPinLoginRes.getCode() != 0) {
                System.out.println("短信登录失败：" + youPinLoginRes.getMsg());
            }
            if (getUserInfo(youPinLoginRes.Data.Token)) {
                return true;
            } else {
                System.out.println("登陆失败");
            }
        } catch (Exception e) {
            System.out.println("解析json失败");
        }
        return false;
    }

    /**
     * 获取用户信息（3）
     *
     * @param token 授权令牌
     * @return 用户信息Map
     */
    @Override
    public boolean getUserInfo(String token) throws IOException {

        String ioToken = readToken();
        // 设置请求参数
        Map<String, String> params = new HashMap<>();
        params.put("source", "2");

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);
        // 发送GET请求
        String response = youPinHttpClient.doGet("user/Account/getUserInfo", params, headers);

        YouPinUserInfoRes youPinUserInfoRes = gson.fromJson(response, YouPinUserInfoRes.class);

        if (youPinUserInfoRes.getData() == null) {
            System.out.println("信息检验失败，请重新登陆绑定");
            throw new RuntimeException("信息检验失败，请重新登陆绑定");
        }

        String steamId = youPinUserInfoRes.getData().getSteamId();

        //判断用户绑定的steam是否在本平台绑定
        List<SteamRes.SteamBind> steamBindList = accountManagerService.getAllSteamAccount(ioToken);
        if (steamBindList == null || steamBindList.isEmpty()) {
            System.out.println("IO661平台未绑定账号，请先绑定后使用");
            throw new RuntimeException("该steam账号未绑定到IO661平台");
        }
        // 获取steamId和当前steam账号绑定的steamId对比
        if (steamBindList.stream().noneMatch(steamBind -> steamBind.getSteamId().equals(steamId))) {
            System.out.println("该steam账号未绑定到IO661平台");
            throw new RuntimeException("该steam账号未绑定到IO661平台");
        }

        // 保存cookie和steamId到数据库
        saveYouPinCookie(steamId, token);
        return true;
    }

    /**
     * 获取用户YouPin库存
     *
     * @param token 授权令牌
     * @return 用户库存Map
     */
    @Override
    public Map<String, Object> getUserInventoryDataList(String token) throws IOException {
        // 设置请求参数
        Map<String, Object> data = new HashMap<>();
        data.put("PageSize", "1000");
        data.put("PageIndex", "1");
        data.put("RefreshType", "1");
        data.put("AppType", "4");
        data.put("IsMerge", "0");
        data.put("GameID", "730");
        data.put("Sort", null);
        data.put("Sessionid", SessionId);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 发送GET请求
        String response = youPinHttpClient.doPost("commodity/Inventory/GetUserInventoryDataListV3", jsonBody, headers);
        Map<String, Object> responseMap = jsonToMap(response);
        if (responseMap.containsKey("Data")) {
            return responseMap;
        }

        return null;
    }

    /**
     * 获取用户YouPin在售
     *
     * @param token 授权令牌
     * @return 用户在售Map
     */
    @Override
    public YouPinUserInventoryOnSellDataListRes getUserInventoryOnSellDataList(String token) throws IOException {
        YouPinUserInventoryOnSellDataListRes result = new YouPinUserInventoryOnSellDataListRes();

        // 设置请求参数
        Map<String, Object> data = new HashMap<>();
        data.put("pageSize", 100);
        data.put("pageIndex", 1);
        data.put("whetherMerge", 0);
        data.put("Sessionid", SessionId);


        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 发送POST请求
        String response = youPinHttpClient.doPost("youpin/bff/new/commodity/v1/commodity/list/sell", jsonBody, headers);

        YouPinUserInventoryOnSellDataListRes responseDataList = gson.fromJson(response, YouPinUserInventoryOnSellDataListRes.class);

        if (!responseDataList.getCode().equals(0)) {
            result.setCode(-1);
            result.setMsg("获取在售列表失败");
            return result;
        }

        result.setCode(responseDataList.getCode());
        result.setMsg(responseDataList.getMsg());
        result.setData(responseDataList.getData());

        return result;
    }


    /**
     * 获取用户YouPin待发货
     *
     * @param token 授权令牌
     * @return 用户在售Map
     */
    @Override
    public Map<String, Object> getUserWaitDeliverList(String token) throws IOException {
        // 设置请求参数
        Map<String, Object> data = new HashMap<>();
        data.put("pageSize", 1000);
        data.put("pageIndex", 1);
        data.put("gameId", 730);
        data.put("Sessionid", SessionId);


        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 发送GET请求
        String response = youPinHttpClient.doPost("youpin/bff/trade/sell/page/v1/waitDeliver/waitDeliverList", jsonBody, headers);
        Map<String, Object> responseMap = jsonToMap(response);
        if (responseMap.containsKey("Data")) {
            return responseMap;
        }

        return null;
    }

    /**
     * 用户YouPin上架饰品
     *
     * @param token 授权令牌
     * @return 返回上架响应数据
     */
    @Override
    public YouPinUserSellInventoryRes userSellInventory(String token, YouPinUserSellInventoryReq youPinUserSellInventoryReq) throws IOException {

        YouPinUserSellInventoryRes result = new YouPinUserSellInventoryRes();

        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();

        // 遍历youPinUserSellInventoryReq
        for (YouPinUserSellInventoryReq.DataItem item : youPinUserSellInventoryReq.getData()) {
            Map<String, Object> map = new HashMap<>();
            map.put("AssetId", item.getAssetId());
            map.put("IsCanSold", true);
            map.put("IsCanLease", false);
            map.put("Remark", item.getRemark());
            // 将IO661的价格（分）转换为悠悠有品的价格（元）
            String uuPriceInYuan = "0.00";
            try {
                uuPriceInYuan =  item.getPrice();
            } catch (NumberFormatException e) {
                throw new RuntimeException("价格格式转换错误：" + item.getPrice());
            }
            map.put("Price", uuPriceInYuan);
            list.add(map);
        }

        data.put("GameID", 730);
        data.put("Sessionid", SessionId);

        data.put("ItemInfos", list);
        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);
        // 发送GET请求
        String response = youPinHttpClient.doPost("commodity/Inventory/SellInventoryWithLeaseV2", jsonBody, headers);
        // 解析响应
        YouPinUserSellInventoryRes responseDataList = gson.fromJson(response, YouPinUserSellInventoryRes.class);

        // 设置基础响应信息
        result.setCode(responseDataList.getCode());
        result.setMsg(responseDataList.getMsg());

        // 解析Data数组
        if (responseDataList.getData() == null) {
            result.setCode(-1);
            result.setMsg("上架失败");
            return result;
        }
        List<YouPinUserSellInventoryRes.DataItem> dataList = responseDataList.getData();
        List<YouPinUserSellInventoryRes.DataItem> resultDataItems = new ArrayList<>();

        for (YouPinUserSellInventoryRes.DataItem itemMap : dataList) {
            YouPinUserSellInventoryRes.DataItem dataItem = new YouPinUserSellInventoryRes.DataItem();
            dataItem.setAssetId(Long.parseLong(String.valueOf(itemMap.getAssetId())));
            dataItem.setCommodityId(Long.parseLong(String.valueOf(itemMap.getCommodityId())));
            dataItem.setCommodityNo(itemMap.getCommodityNo());
            dataItem.setStatus(itemMap.getStatus());
            dataItem.setRemark(itemMap.getRemark());
            resultDataItems.add(dataItem);
            result.setData(resultDataItems);
        }
        return result;
    }

    /**
     * 用户YouPin改价饰品
     *
     * @param token 授权令牌
     * @param request 改价请求
     * @return 改价响应
     */
    @Override
    public YouPinUserItemsOnSellPriceChangeRes userItemsOnSellPriceChange(String token, YouPinUserItemsOnSellPriceChangeReq request) throws IOException {
        // 设置请求参数
        Map<String, Object> data = new HashMap<>();

        // 设置基本参数
        data.put("GameID", request.getGameID() != null ? request.getGameID() : 730);
        data.put("Sessionid", request.getSessionid() != null ? request.getSessionid() : SessionId);

        // 转换商品列表
        List<Map<String, Object>> commoditysList = new ArrayList<>();
        if (request.getCommoditys() != null) {
            for (YouPinUserItemsOnSellPriceChangeReq.Commodity commodity : request.getCommoditys()) {
                Map<String, Object> commodityMap = new HashMap<>();
                commodityMap.put("CommodityId", commodity.getCommodityId());
                commodityMap.put("IsCanSold", commodity.getIsCanSold() != null ? commodity.getIsCanSold() : true);
                commodityMap.put("OnlyChangeSaleInfo", commodity.getOnlyChangeSaleInfo() != null ? commodity.getOnlyChangeSaleInfo() : true);
                commodityMap.put("Remark", commodity.getRemark() != null ? commodity.getRemark() : "");
                // 改价接口中价格已经是字符串格式（元），直接使用
                String uuPriceInYuan = commodity.getPrice() != null ? commodity.getPrice() : "0.00";
                commodityMap.put("Price", uuPriceInYuan);
                commoditysList.add(commodityMap);
            }
        }
        data.put("Commoditys", commoditysList);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 发送PUT请求
        String response = youPinHttpClient.doPut("commodity/Commodity/PriceChangeWithLeaseV2", jsonBody, headers);

        // 解析响应
        return gson.fromJson(response, YouPinUserItemsOnSellPriceChangeRes.class);
    }

    /**
     * 用户YouPin下架饰品
     * @param token 授权令牌
     * @return 上架成功失败
     */
    @Override
    public boolean userItemsOffSale(String token, YouPinUserItemsOffSaleReq youPinUserItemsOffSaleReq) throws IOException {
        // 设置请求参数
        Map<String, Object> data = new HashMap<>();

        // 正确处理ids列表
        List<String> idsList = youPinUserItemsOffSaleReq.getIds().stream()
            .map(YouPinUserItemsOffSaleReq.Ids::getId)
            .toList();

        data.put("Ids", idsList);
        data.put("IsDeleteCommodityCache", 1);
        data.put("IsForceOffline", true);
        data.put("Sessionid", SessionId);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 发送PUT请求
        String response = youPinHttpClient.doPut("commodity/Commodity/OffShelf", jsonBody, headers);
        Map<String, Object> responseMap = jsonToMap(response);

        // 检查响应状态
        if (responseMap.containsKey("Code")) {
            Integer code = (Integer) responseMap.get("Code");
            return code == 0; // 返回true表示下架成功
        }

        return false;
    }


    /**
     * 发送校验验证码(4)
     *
     * @param userId 用户ID
     * @param token  授权令牌
     * @return 返回结果数组，包含[状态码, 消息, 会话ID]
     */
    @Override
    public String[] sendCode(String userId, String token) throws IOException {
        // 设置请求头
        Map<String, String> headers = new HashMap<>();

        // 设置请求体
        Map<String, Object> data = new HashMap<>();
        data.put("mobile", "");
        data.put("scene", 15);
        data.put("userId", userId);
        data.put("Sessionid", SessionId);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 发送POST请求
        String response = youPinHttpClient.doPost("youpin/bff/trade/v1/user/presenter/sendSmsCode", jsonBody, headers);

        // 解析响应
        Map<String, Object> responseMap = jsonToMap(response);

        if (responseMap != null) {
            String code = String.valueOf(responseMap.get("code"));
            String msg = (String) responseMap.get("msg");

            String sessionId = "";
            if (responseMap.get("data") != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) responseMap.get("data");
                if (responseData != null && responseData.containsKey("sessionId")) {
                    sessionId = (String) responseData.get("sessionId");
                }
            }
            return new String[]{code, msg, sessionId};
        }

        return new String[]{"", "", ""};
    }

    /**
     * 获取商品信息（5）
     *
     * @param token 授权令牌
     * @return 返回商品ID和价格的数组，如果获取失败则返回null
     */
    @Override
    public Object[] getGoodsInfo(String token) throws IOException {
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 设置请求体
        Map<String, Object> data = new HashMap<>();
        data.put("templateId", "45242");

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 发送POST请求
        String response = youPinHttpClient.doPost("homepage/v2/detail/commodity/list/sell", jsonBody, headers);

        // 解析响应
        Map<String, Object> responseMap = jsonToMap(response);

        if (responseMap != null && responseMap.containsKey("Data")) {
            Object dataObj = responseMap.get("Data");
            if (dataObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) dataObj;

                if (responseData.containsKey("CommodityList")) {
                    Object listObj = responseData.get("CommodityList");
                    if (listObj instanceof List && !((List<?>) listObj).isEmpty()) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> commodityList = (List<Map<String, Object>>) listObj;

                        // 获取第一个商品信息
                        Map<String, Object> sellInfo = commodityList.getFirst();

                        // 获取商品ID
                        String id = null;
                        Object idObj = sellInfo.get("Id");
                        if (idObj instanceof String) {
                            id = (String) idObj;
                        }

                        // 获取价格并转换为整数（乘以100）
                        Integer priceInCents = null;
                        Object priceObj = sellInfo.get("Price");
                        if (priceObj instanceof String) {
                            try {
                                double price = Double.parseDouble((String) priceObj);
                                priceInCents = (int) Math.round(price * 100);
                            } catch (NumberFormatException e) {
                                System.out.println(e.getMessage());
                            }
                        }

                        // 返回结果
                        if (id != null && priceInCents != null) {
                            return new Object[]{id, priceInCents};
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 确认支付订单（6）
     *
     * @param orderNo           订单号
     * @param payOrderNo        支付订单号
     * @param waitPaymentDataNo 等待支付数据号
     * @param goodsPrice        商品价格（单位：分）
     * @param token             授权令牌
     * @return 返回结果数组，包含[状态码, 消息]
     * @throws IOException 请求异常
     */
    @Override
    public String[] confirmOrder(String orderNo, String payOrderNo, String waitPaymentDataNo,
                                 int goodsPrice, String token) throws IOException {
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);

        // 设置请求体
        Map<String, Object> data = new HashMap<>();
        data.put("businessType", "1");
        data.put("channelId", "100");
        data.put("extend", "{\"activityCode\":\"PTGM00000\",\"appInstall\":false}");
        data.put("orderNo", orderNo);
        data.put("outTradeNo", payOrderNo);
        data.put("payWay", 7);
        data.put("paymentAmount", goodsPrice / 100.0);
        data.put("subBusType", "21000");
        data.put("waitPaymentDataNo", waitPaymentDataNo);

        // 将Map转换为JSON字符串
        String jsonBody = mapToJson(data);

        // 发送POST请求
        String response = youPinHttpClient.doPost("youpin/bff/payment/v1/pay/order/confirm", jsonBody, headers);

        // 解析响应
        Map<String, Object> responseMap = jsonToMap(response);

        if (responseMap != null) {
            String code = String.valueOf(responseMap.get("code"));
            String msg = (String) responseMap.get("msg");

            return new String[]{code, msg};
        }

        return new String[]{"", ""};
    }

    /**
     * 校验验证码并创建订单（7）
     *
     * @param sessionId 会话ID
     * @param code      验证码
     * @param token     授权令牌
     * @return 返回结果数组，包含[状态码, 消息]
     */
    @Override
    public String[] checkCode(String sessionId, String code, String token) throws IOException {
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        // 添加Authorization头
        youPinHttpClient.addAuthorizationHeader(headers, token);
        Map<String, Object> data = new HashMap<>();

        data.put("commodityId", "45242");
        data.put("sellAmount", 1);
        data.put("payAmount", 1);
        data.put("payGiveSwitch", 1);
        data.put("orderSubType", 5);
        data.put("sessionId", sessionId);
        data.put("smsCode", code);
        data.put("tradeLinks", "");
        data.put("Sessionid", "ZuwCIRukq7IDACT0\\/DzGc0Rj");
        String jsonBody = mapToJson(data);
        // 发送POST请求
        String response = youPinHttpClient.doPost("youpin/bff/trade/v1/order/sell/create-giving", jsonBody, headers);

        // 解析响应
        Map<String, Object> responseMap = jsonToMap(response);

        return null;
    }

    /**
     * 将Map转换为JSON字符串
     *
     * @param map 要转换的Map
     * @return JSON字符串
     */
    private String mapToJson(Map<String, Object> map) {
        return gson.toJson(map);
    }

    /**
     * 将JSON字符串转换为Map
     *
     * @param json JSON字符串
     * @return Map对象
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> jsonToMap(String json) {
        try {
            return gson.fromJson(json, Map.class);
        } catch (Exception e) {
            System.err.println("JSON解析错误: " + e.getMessage());
            return new HashMap<>();
        }
    }
}